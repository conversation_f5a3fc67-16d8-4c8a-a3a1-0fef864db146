"""
角色服务

提供角色管理的业务逻辑实现
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Type

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    DatabaseError, BusinessError
)
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission,
    AuditLog, AuditLogBuilder
)


class RoleService:
    """角色服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        user_model: Type[User],
        tenant_model: Type[Tenant],
        role_model: Type[Role],
        permission_model: Type[Permission],
        user_role_model: Type[UserRole],
        role_permission_model: Type[RolePermission],
        audit_log_model: Type[AuditLog]
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 核心业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.role_model = role_model
        self.permission_model = permission_model

        # 关联关系模型
        self.user_role_model = user_role_model
        self.role_permission_model = role_permission_model

        # 审计模型
        self.audit_log_model = audit_log_model

        # 角色状态定义
        self.ROLE_STATUS = {
            "ACTIVE": "active",
            "INACTIVE": "inactive",
            "DELETED": "deleted"
        }

        # 状态转换规则
        self.STATUS_TRANSITIONS = {
            "active": ["inactive", "deleted"],
            "inactive": ["active", "deleted"],
            "deleted": []  # 已删除状态无法转换
        }

    async def create_role(
        self,
        role_name: str,
        role_code: str,
        description: Optional[str],
        tenant_id: str,
        permission_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """创建角色"""
        try:
            # 验证租户存在性和创建权限
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant or tenant.status != CommonStatus.ACTIVE:
                raise NotFoundError("租户不存在或未激活")

            # 检查角色名称和编码唯一性
            await self._validate_role_uniqueness(tenant_id, role_name, role_code)

            # 验证权限ID的有效性
            valid_permission_ids = []
            if permission_ids:
                valid_permission_ids = await self._validate_permissions_exist(tenant_id, permission_ids)

            # 生成角色ID
            role_id = f"role_{uuid.uuid4()}"

            # 创建角色记录到数据库
            role = self.role_model(
                role_id=role_id,
                tenant_id=tenant_id,
                role_name=role_name,
                role_code=role_code,
                description=description,
                status=CommonStatus.ACTIVE,
                level=1,  # 默认层级
                meta_data={},
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            self.session.add(role)
            await self.session.flush()

            # 建立角色权限关联关系
            assigned_permissions = []
            if valid_permission_ids:
                assigned_permissions = await self._assign_permissions_to_role(
                    role_id, tenant_id, valid_permission_ids
                )

            # 记录角色创建审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="CREATE_ROLE",
                resource_type="ROLE",
                resource_id=role_id,
                details={
                    "role_name": role_name,
                    "role_code": role_code,
                    "permission_count": len(assigned_permissions)
                }
            )

            await self.session.commit()

            # 缓存角色基本信息
            await self._cache_role_info(role_id, {
                "role_id": role_id,
                "role_name": role_name,
                "role_code": role_code,
                "status": CommonStatus.ACTIVE,
                "tenant_id": tenant_id
            })

            return {
                "role_id": role_id,
                "role_name": role_name,
                "role_code": role_code,
                "description": description,
                "tenant_id": tenant_id,
                "status": CommonStatus.ACTIVE,
                "level": 1,
                "permissions": assigned_permissions,
                "user_count": 0,
                "created_at": role.created_at.isoformat(),
                "updated_at": role.updated_at.isoformat()
            }

        except IntegrityError as e:
            await self.session.rollback()
            if "role_code" in str(e):
                raise DuplicateResourceError("角色编码已存在", "role_code", role_code)
            elif "role_name" in str(e):
                raise DuplicateResourceError("角色名称已存在", "role_name", role_name)
            else:
                raise DatabaseError("数据库约束错误")
        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"创建角色失败: {str(e)}")

    async def list_roles(
        self,
        tenant_id: str,
        limit: int = 20,
        search: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取角色列表"""
        try:
            # 验证租户权限和访问控制
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant:
                raise NotFoundError("租户不存在")

            # 构建查询条件（搜索、状态筛选）
            conditions = [
                self.role_model.tenant_id == tenant_id,
                self.role_model.status != "deleted"
            ]

            # 添加状态筛选
            if status:
                conditions.append(self.role_model.status == status)

            # 添加搜索条件
            if search:
                search_condition = or_(
                    self.role_model.role_name.ilike(f"%{search}%"),
                    self.role_model.role_code.ilike(f"%{search}%"),
                    self.role_model.description.ilike(f"%{search}%")
                )
                conditions.append(search_condition)

            # 查询角色基本信息
            query = select(self.role_model).where(and_(*conditions)).order_by(
                self.role_model.level, self.role_model.created_at
            ).limit(limit)

            result = await self.session.execute(query)
            roles = result.scalars().all()

            # 获取角色统计信息
            role_ids = [role.role_id for role in roles]
            role_stats = await self._get_roles_statistics(role_ids)

            # 格式化返回数据
            items = []
            for role in roles:
                stats = role_stats.get(role.role_id, {"user_count": 0, "permission_count": 0})

                items.append({
                    "role_id": role.role_id,
                    "role_name": role.role_name,
                    "role_code": role.role_code,
                    "description": role.description,
                    "level": role.level,
                    "parent_role_id": role.parent_role_id,
                    "status": role.status,
                    "user_count": stats["user_count"],
                    "permission_count": stats["permission_count"],
                    "max_users": role.max_users,
                    "meta_data": role.meta_data or {},
                    "created_at": role.created_at.isoformat(),
                    "updated_at": role.updated_at.isoformat() if role.updated_at else None
                })

            # 获取总数
            count_query = select(func.count()).select_from(
                select(self.role_model.role_id).where(and_(*conditions)).subquery()
            )
            count_result = await self.session.execute(count_query)
            total = count_result.scalar()

            return {
                "roles": items,
                "total": total,
                "limit": limit
            }

        except Exception as e:
            raise BusinessError(f"查询角色列表失败: {str(e)}")

    async def get_role_detail(self, role_id: str, tenant_id: str) -> Dict[str, Any]:
        """获取角色详情"""
        try:
            # 验证角色存在性和访问权限
            role = await self._get_role_by_id(role_id, tenant_id)
            if not role:
                raise NotFoundError("角色不存在")

            # 获取角色权限列表
            permissions = await self._get_role_permissions(role_id)

            # 查询角色层级关系
            parent_role = None
            if role.parent_role_id:
                parent_role = await self._get_role_by_id(role.parent_role_id, tenant_id)

            child_roles = await self._get_child_roles(role_id, tenant_id)

            # 统计关联用户数量
            user_count = await self._count_role_users(role_id)

            # 获取角色统计信息
            stats = await self._get_role_statistics(role_id)

            return {
                "role_id": role.role_id,
                "role_name": role.role_name,
                "role_code": role.role_code,
                "description": role.description,
                "level": role.level,
                "parent_role_id": role.parent_role_id,
                "parent_role": {
                    "role_id": parent_role.role_id,
                    "role_name": parent_role.role_name,
                    "role_code": parent_role.role_code
                } if parent_role else None,
                "child_roles": [
                    {
                        "role_id": child.role_id,
                        "role_name": child.role_name,
                        "role_code": child.role_code
                    } for child in child_roles
                ],
                "status": role.status,
                "max_users": role.max_users,
                "meta_data": role.meta_data or {},
                "permissions": permissions,
                "statistics": {
                    "user_count": user_count,
                    "permission_count": len(permissions),
                    "direct_permission_count": stats.get("direct_permission_count", 0),
                    "inherited_permission_count": stats.get("inherited_permission_count", 0)
                },
                "created_at": role.created_at.isoformat(),
                "updated_at": role.updated_at.isoformat() if role.updated_at else None
            }

        except Exception as e:
            raise BusinessError(f"获取角色详情失败: {str(e)}")
    
    async def update_role(
        self,
        role_id: str,
        tenant_id: str,
        role_name: Optional[str] = None,
        description: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """更新角色信息"""
        try:
            # 验证角色存在性
            role = await self._get_role_by_id(role_id, tenant_id)
            if not role:
                raise NotFoundError("角色不存在")

            # 记录原始值用于审计
            original_values = {
                "role_name": role.role_name,
                "description": role.description,
                "status": role.status
            }

            # 检查角色名称唯一性（如果要更新名称）
            if role_name and role_name != role.role_name:
                await self._check_role_name_uniqueness(tenant_id, role_name, role_id)
                role.role_name = role_name

            # 更新描述
            if description is not None:
                role.description = description

            # 验证状态变更的合法性并更新
            if status and status != role.status:
                await self._validate_status_transition(role.status, status)
                role.status = status

            # 更新时间戳
            role.updated_at = datetime.now()

            await self.session.commit()

            # 清理角色缓存
            await self._clear_role_cache(role_id)

            # 记录修改审计日志
            changes = {}
            for key, original_value in original_values.items():
                new_value = getattr(role, key)
                if original_value != new_value:
                    changes[key] = {"old": original_value, "new": new_value}

            if changes:
                await self._create_audit_log(
                    tenant_id=tenant_id,
                    user_id=None,  # TODO: 从上下文获取操作者ID
                    action="UPDATE_ROLE",
                    resource_type="ROLE",
                    resource_id=role_id,
                    details={"changes": changes}
                )

            # 获取更新后的权限列表
            permissions = await self._get_role_permissions(role_id)
            user_count = await self._count_role_users(role_id)

            return {
                "role_id": role.role_id,
                "role_name": role.role_name,
                "role_code": role.role_code,
                "description": role.description,
                "status": role.status,
                "level": role.level,
                "permissions": permissions,
                "user_count": user_count,
                "created_at": role.created_at.isoformat(),
                "updated_at": role.updated_at.isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更新角色信息失败: {str(e)}")

    async def delete_role(
        self,
        role_id: str,
        tenant_id: str,
        force: bool = False
    ) -> Dict[str, Any]:
        """删除角色"""
        try:
            # 验证角色存在性
            role = await self._get_role_by_id(role_id, tenant_id)
            if not role:
                raise NotFoundError("角色不存在")

            # 检查角色依赖关系
            dependencies = await self._check_role_dependencies(role_id)
            if dependencies["has_dependencies"] and not force:
                raise ValidationError(f"角色存在依赖关系，无法删除: {dependencies['dependencies']}")

            # 统计清理信息
            user_count = await self._count_role_users(role_id)
            permission_count = await self._count_role_permissions(role_id)

            # 移除所有用户角色关联
            users_unassigned = await self._remove_all_role_users(role_id, tenant_id)

            # 移除所有角色权限关联
            permissions_removed = await self._remove_all_role_permissions(role_id, tenant_id)

            # 检查子角色
            child_roles = await self._get_child_roles(role_id, tenant_id)
            if child_roles and not force:
                raise ValidationError("角色存在子角色，无法删除。请先删除子角色或使用强制删除")

            # 处理子角色的父角色关系
            for child_role in child_roles:
                child_role.parent_role_id = role.parent_role_id
                await self.session.flush()

            # 软删除角色
            role.status = "deleted"
            role.updated_at = datetime.now()

            await self.session.commit()

            # 清理角色缓存
            await self._clear_role_cache(role_id)

            # 记录删除审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="DELETE_ROLE",
                resource_type="ROLE",
                resource_id=role_id,
                details={
                    "role_name": role.role_name,
                    "role_code": role.role_code,
                    "force": force,
                    "users_unassigned": users_unassigned,
                    "permissions_removed": permissions_removed,
                    "child_roles_updated": len(child_roles)
                }
            )

            return {
                "success": True,
                "role_id": role_id,
                "force": force,
                "deleted_at": datetime.now().isoformat(),
                "cleanup_summary": {
                    "users_unassigned": users_unassigned,
                    "permissions_removed": permissions_removed,
                    "child_roles_updated": len(child_roles)
                }
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"删除角色失败: {str(e)}")

    async def assign_permissions(
        self,
        role_id: str,
        tenant_id: str,
        permission_ids: List[str]
    ) -> Dict[str, Any]:
        """分配角色权限"""
        try:
            # 验证角色存在性
            role = await self._get_role_by_id(role_id, tenant_id)
            if not role:
                raise NotFoundError("角色不存在")

            # 验证权限存在性和有效性
            valid_permissions = await self._validate_permissions_exist(tenant_id, permission_ids)

            # 检查角色是否已经拥有这些权限
            existing_permissions = await self._get_role_existing_permissions(role_id, permission_ids)
            new_permissions = [p_id for p_id in permission_ids if p_id not in existing_permissions]

            if not new_permissions:
                return {
                    "success": True,
                    "role_id": role_id,
                    "assigned_permissions": [],
                    "failed_permissions": [{"permission_id": p_id, "reason": "权限已分配"} for p_id in permission_ids],
                    "assigned_at": datetime.now().isoformat()
                }

            # 分配权限
            assigned_permissions = []
            failed_permissions = []

            for permission_id in new_permissions:
                try:
                    # 创建角色权限关联
                    role_permission = self.role_permission_model(
                        role_id=role_id,
                        permission_id=permission_id,
                        tenant_id=tenant_id,
                        status=CommonStatus.ACTIVE,
                        assigned_at=datetime.now(),
                        assigned_by=None  # TODO: 从上下文获取操作者ID
                    )
                    self.session.add(role_permission)

                    # 获取权限信息
                    permission_info = await self._get_permission_info(permission_id)
                    assigned_permissions.append({
                        "permission_id": permission_id,
                        "permission_name": permission_info.get("name", ""),
                        "permission_code": permission_info.get("code", ""),
                        "resource": permission_info.get("resource", ""),
                        "action": permission_info.get("action", ""),
                        "assigned_at": datetime.now().isoformat()
                    })

                except Exception as e:
                    failed_permissions.append({
                        "permission_id": permission_id,
                        "reason": f"分配失败: {str(e)}"
                    })

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="ASSIGN_ROLE_PERMISSIONS",
                resource_type="ROLE",
                resource_id=role_id,
                details={
                    "permission_ids": new_permissions,
                    "assigned_count": len(assigned_permissions),
                    "failed_count": len(failed_permissions)
                }
            )

            await self.session.commit()

            # 清除角色权限缓存
            await self._clear_role_permissions_cache(role_id)

            return {
                "success": True,
                "role_id": role_id,
                "assigned_permissions": assigned_permissions,
                "failed_permissions": failed_permissions,
                "assigned_at": datetime.now().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"分配权限失败: {str(e)}")

    async def remove_permissions(
        self,
        role_id: str,
        tenant_id: str,
        permission_ids: List[str]
    ) -> Dict[str, Any]:
        """移除角色权限"""
        try:
            # 验证角色存在性
            role = await self._get_role_by_id(role_id, tenant_id)
            if not role:
                raise NotFoundError("角色不存在")

            # 检查角色当前拥有的权限
            role_permissions = await self._get_role_existing_permissions(role_id, permission_ids)
            permissions_to_remove = [p_id for p_id in permission_ids if p_id in role_permissions]

            if not permissions_to_remove:
                return {
                    "success": True,
                    "role_id": role_id,
                    "removed_permissions": [],
                    "failed_permissions": [{"permission_id": p_id, "reason": "角色未拥有此权限"} for p_id in permission_ids],
                    "removed_at": datetime.now().isoformat()
                }

            # 移除权限
            removed_permissions = []
            failed_permissions = []

            for permission_id in permissions_to_remove:
                try:
                    # 删除角色权限关联
                    stmt = select(self.role_permission_model).where(
                        and_(
                            self.role_permission_model.role_id == role_id,
                            self.role_permission_model.permission_id == permission_id,
                            self.role_permission_model.tenant_id == tenant_id
                        )
                    )
                    result = await self.session.execute(stmt)
                    role_permission = result.scalar_one_or_none()

                    if role_permission:
                        await self.session.delete(role_permission)

                        # 获取权限信息
                        permission_info = await self._get_permission_info(permission_id)
                        removed_permissions.append({
                            "permission_id": permission_id,
                            "permission_name": permission_info.get("name", ""),
                            "permission_code": permission_info.get("code", ""),
                            "removed_at": datetime.now().isoformat()
                        })
                    else:
                        failed_permissions.append({
                            "permission_id": permission_id,
                            "reason": "权限关联不存在"
                        })

                except Exception as e:
                    failed_permissions.append({
                        "permission_id": permission_id,
                        "reason": f"移除失败: {str(e)}"
                    })

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="REMOVE_ROLE_PERMISSIONS",
                resource_type="ROLE",
                resource_id=role_id,
                details={
                    "permission_ids": permissions_to_remove,
                    "removed_count": len(removed_permissions),
                    "failed_count": len(failed_permissions)
                }
            )

            await self.session.commit()

            # 清除角色权限缓存
            await self._clear_role_permissions_cache(role_id)

            return {
                "success": True,
                "role_id": role_id,
                "removed_permissions": removed_permissions,
                "failed_permissions": failed_permissions,
                "removed_at": datetime.now().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"移除权限失败: {str(e)}")
    
    async def assign_users(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分配角色给用户"""
        try:
            role_id = data.get("role_id")
            user_ids = data.get("user_ids", [])
            tenant_id = data.get("tenant_id")

            # 验证角色存在性
            role = await self._get_role_by_id(role_id, tenant_id)
            if not role:
                raise NotFoundError("角色不存在")

            # 验证用户存在性
            valid_users = await self._validate_users_exist(tenant_id, user_ids)

            # 检查角色用户数量限制
            if role.max_users > 0:
                current_user_count = await self._count_role_users(role_id)
                if current_user_count + len(valid_users) > role.max_users:
                    raise ValidationError(f"角色用户数量超过限制 ({role.max_users})")

            # 分配角色给用户
            assigned_count = 0
            for user_id in valid_users:
                # 检查用户是否已拥有此角色
                existing = await self._check_user_role_exists(user_id, role_id)
                if not existing:
                    user_role = self.user_role_model(
                        user_id=user_id,
                        role_id=role_id,
                        tenant_id=tenant_id,
                        assignment_type="permanent",
                        status=CommonStatus.ACTIVE,
                        assigned_at=datetime.now()
                    )
                    self.session.add(user_role)
                    assigned_count += 1

            await self.session.commit()

            return {
                "success": True,
                "role_id": role_id,
                "user_ids": valid_users,
                "assigned_count": assigned_count,
                "assigned_at": datetime.now().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"分配用户角色失败: {str(e)}")

    async def remove_users(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """移除用户角色"""
        try:
            role_id = data.get("role_id")
            user_ids = data.get("user_ids", [])
            tenant_id = data.get("tenant_id")

            # 验证角色存在性
            role = await self._get_role_by_id(role_id, tenant_id)
            if not role:
                raise NotFoundError("角色不存在")

            # 移除用户角色
            removed_count = 0
            for user_id in user_ids:
                stmt = select(self.user_role_model).where(
                    and_(
                        self.user_role_model.user_id == user_id,
                        self.user_role_model.role_id == role_id,
                        self.user_role_model.tenant_id == tenant_id
                    )
                )
                result = await self.session.execute(stmt)
                user_role = result.scalar_one_or_none()

                if user_role:
                    await self.session.delete(user_role)
                    removed_count += 1

            await self.session.commit()

            return {
                "success": True,
                "role_id": role_id,
                "user_ids": user_ids,
                "removed_count": removed_count,
                "removed_at": datetime.now().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"移除用户角色失败: {str(e)}")

    # ===== 辅助方法实现 =====

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _validate_role_uniqueness(self, tenant_id: str, role_name: str, role_code: str):
        """验证角色唯一性"""
        # 检查角色名称唯一性
        stmt = select(self.role_model).where(
            and_(
                self.role_model.tenant_id == tenant_id,
                self.role_model.role_name == role_name,
                self.role_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("角色名称已存在", "role_name", role_name)

        # 检查角色编码唯一性
        stmt = select(self.role_model).where(
            and_(
                self.role_model.tenant_id == tenant_id,
                self.role_model.role_code == role_code,
                self.role_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("角色编码已存在", "role_code", role_code)

    async def _validate_permissions_exist(self, tenant_id: str, permission_ids: List[str]) -> List[str]:
        """验证权限存在性和有效性"""
        stmt = select(self.permission_model).where(
            and_(
                self.permission_model.permission_id.in_(permission_ids),
                self.permission_model.tenant_id == tenant_id,
                self.permission_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        valid_permissions = result.scalars().all()

        valid_permission_ids = [perm.permission_id for perm in valid_permissions]
        invalid_permissions = [p_id for p_id in permission_ids if p_id not in valid_permission_ids]

        if invalid_permissions:
            raise NotFoundError(f"以下权限不存在或未激活: {invalid_permissions}")

        return valid_permission_ids

    async def _assign_permissions_to_role(self, role_id: str, tenant_id: str, permission_ids: List[str]) -> List[Dict[str, Any]]:
        """为角色分配权限"""
        assigned_permissions = []

        for permission_id in permission_ids:
            # 检查权限是否已分配
            existing = await self._check_role_permission_exists(role_id, permission_id)
            if not existing:
                role_permission = self.role_permission_model(
                    role_id=role_id,
                    permission_id=permission_id,
                    tenant_id=tenant_id,
                    status=CommonStatus.ACTIVE,
                    assigned_at=datetime.now()
                )
                self.session.add(role_permission)

                # 获取权限信息
                permission_info = await self._get_permission_info(permission_id)
                assigned_permissions.append({
                    "permission_id": permission_id,
                    "permission_name": permission_info.get("name", ""),
                    "permission_code": permission_info.get("code", ""),
                    "resource": permission_info.get("resource", ""),
                    "action": permission_info.get("action", "")
                })

        return assigned_permissions

    async def _create_audit_log(self, tenant_id: str, user_id: Optional[str], action: str, resource_type: str, resource_id: str, details: Dict[str, Any]):
        """创建审计日志"""
        try:
            audit_log = AuditLogBuilder.create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details,
                result="SUCCESS"
            )
            self.session.add(audit_log)
            await self.session.flush()
        except Exception as e:
            # 审计日志失败不应该影响主业务流程
            print(f"审计日志记录失败: {str(e)}")

    async def _cache_role_info(self, role_id: str, role_info: Dict[str, Any]):
        """缓存角色信息"""
        await self.redis_repo.set(
            f"role_info:{role_id}",
            role_info,
            ttl=3600  # 1小时过期
        )

    async def _get_role_by_id(self, role_id: str, tenant_id: str):
        """根据ID获取角色"""
        stmt = select(self.role_model).where(
            and_(
                self.role_model.role_id == role_id,
                self.role_model.tenant_id == tenant_id,
                self.role_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_roles_statistics(self, role_ids: List[str]) -> Dict[str, Dict[str, int]]:
        """批量获取角色统计信息"""
        if not role_ids:
            return {}

        stats = {}

        # 查询用户数量
        user_count_stmt = select(
            self.user_role_model.role_id,
            func.count(self.user_role_model.user_id).label("user_count")
        ).where(
            and_(
                self.user_role_model.role_id.in_(role_ids),
                self.user_role_model.status == CommonStatus.ACTIVE
            )
        ).group_by(self.user_role_model.role_id)

        user_result = await self.session.execute(user_count_stmt)
        user_counts = {row.role_id: row.user_count for row in user_result}

        # 查询权限数量
        perm_count_stmt = select(
            self.role_permission_model.role_id,
            func.count(self.role_permission_model.permission_id).label("permission_count")
        ).where(
            and_(
                self.role_permission_model.role_id.in_(role_ids),
                self.role_permission_model.status == CommonStatus.ACTIVE
            )
        ).group_by(self.role_permission_model.role_id)

        perm_result = await self.session.execute(perm_count_stmt)
        perm_counts = {row.role_id: row.permission_count for row in perm_result}

        # 组合统计信息
        for role_id in role_ids:
            stats[role_id] = {
                "user_count": user_counts.get(role_id, 0),
                "permission_count": perm_counts.get(role_id, 0)
            }

        return stats

    async def _get_role_permissions(self, role_id: str) -> List[Dict[str, Any]]:
        """获取角色权限列表"""
        stmt = select(
            self.role_permission_model.permission_id,
            self.permission_model.permission_name,
            self.permission_model.permission_code,
            self.permission_model.resource,
            self.permission_model.action,
            self.permission_model.description,
            self.role_permission_model.assigned_at
        ).join(
            self.permission_model,
            self.role_permission_model.permission_id == self.permission_model.permission_id
        ).where(
            and_(
                self.role_permission_model.role_id == role_id,
                self.role_permission_model.status == CommonStatus.ACTIVE,
                self.permission_model.status == CommonStatus.ACTIVE
            )
        )

        result = await self.session.execute(stmt)
        rows = result.all()

        return [
            {
                "permission_id": row.permission_id,
                "permission_name": row.permission_name,
                "permission_code": row.permission_code,
                "resource": row.resource,
                "action": row.action,
                "description": row.description,
                "assigned_at": row.assigned_at.isoformat() if row.assigned_at else None
            }
            for row in rows
        ]

    async def _get_child_roles(self, role_id: str, tenant_id: str) -> List:
        """获取子角色列表"""
        stmt = select(self.role_model).where(
            and_(
                self.role_model.parent_role_id == role_id,
                self.role_model.tenant_id == tenant_id,
                self.role_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def _count_role_users(self, role_id: str) -> int:
        """统计角色用户数量"""
        stmt = select(func.count()).where(
            and_(
                self.user_role_model.role_id == role_id,
                self.user_role_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar() or 0

    async def _get_role_statistics(self, role_id: str) -> Dict[str, Any]:
        """获取角色统计信息"""
        # 直接权限数量
        direct_perm_stmt = select(func.count()).where(
            and_(
                self.role_permission_model.role_id == role_id,
                self.role_permission_model.status == CommonStatus.ACTIVE
            )
        )
        direct_result = await self.session.execute(direct_perm_stmt)
        direct_permission_count = direct_result.scalar() or 0

        # TODO: 计算继承权限数量
        inherited_permission_count = 0

        return {
            "direct_permission_count": direct_permission_count,
            "inherited_permission_count": inherited_permission_count
        }

    async def _check_role_name_uniqueness(self, tenant_id: str, role_name: str, exclude_role_id: str):
        """检查角色名称唯一性（排除指定角色）"""
        stmt = select(self.role_model).where(
            and_(
                self.role_model.tenant_id == tenant_id,
                self.role_model.role_name == role_name,
                self.role_model.role_id != exclude_role_id,
                self.role_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("角色名称已存在", "role_name", role_name)

    async def _validate_status_transition(self, current_status: str, new_status: str):
        """验证状态转换的合法性"""
        allowed_transitions = self.STATUS_TRANSITIONS.get(current_status, [])
        if new_status not in allowed_transitions:
            raise ValidationError(f"不允许从状态 {current_status} 转换到 {new_status}")

    async def _clear_role_cache(self, role_id: str):
        """清理角色相关缓存"""
        cache_keys = [
            f"role_info:{role_id}",
            f"role_permissions:{role_id}",
            f"role_users:{role_id}"
        ]
        for key in cache_keys:
            await self.redis_repo.delete(key)

    async def _check_role_dependencies(self, role_id: str) -> Dict[str, Any]:
        """检查角色依赖关系"""
        dependencies = []

        # 检查是否有用户拥有此角色
        user_count = await self._count_role_users(role_id)
        if user_count > 0:
            dependencies.append(f"有 {user_count} 个用户拥有此角色")

        # 检查是否有子角色
        child_roles = await self._get_child_roles(role_id, "")  # tenant_id会在实际调用时传入
        if child_roles:
            dependencies.append(f"存在 {len(child_roles)} 个子角色")

        return {
            "has_dependencies": len(dependencies) > 0,
            "dependencies": dependencies
        }

    async def _remove_all_role_users(self, role_id: str, tenant_id: str) -> int:
        """移除角色的所有用户"""
        stmt = select(self.user_role_model).where(
            and_(
                self.user_role_model.role_id == role_id,
                self.user_role_model.tenant_id == tenant_id
            )
        )
        result = await self.session.execute(stmt)
        user_roles = result.scalars().all()

        count = 0
        for user_role in user_roles:
            await self.session.delete(user_role)
            count += 1

        return count

    async def _remove_all_role_permissions(self, role_id: str, tenant_id: str) -> int:
        """移除角色的所有权限"""
        stmt = select(self.role_permission_model).where(
            and_(
                self.role_permission_model.role_id == role_id,
                self.role_permission_model.tenant_id == tenant_id
            )
        )
        result = await self.session.execute(stmt)
        role_permissions = result.scalars().all()

        count = 0
        for role_permission in role_permissions:
            await self.session.delete(role_permission)
            count += 1

        return count

    async def _get_role_existing_permissions(self, role_id: str, permission_ids: List[str]) -> List[str]:
        """获取角色已拥有的权限"""
        stmt = select(self.role_permission_model.permission_id).where(
            and_(
                self.role_permission_model.role_id == role_id,
                self.role_permission_model.permission_id.in_(permission_ids),
                self.role_permission_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        return [row.permission_id for row in result]

    async def _get_permission_info(self, permission_id: str) -> Dict[str, Any]:
        """获取权限信息"""
        stmt = select(self.permission_model).where(
            self.permission_model.permission_id == permission_id
        )
        result = await self.session.execute(stmt)
        permission = result.scalar_one_or_none()

        if permission:
            return {
                "name": permission.permission_name,
                "code": permission.permission_code,
                "resource": permission.resource,
                "action": permission.action,
                "description": permission.description
            }
        return {}

    async def _clear_role_permissions_cache(self, role_id: str):
        """清除角色权限缓存"""
        await self.redis_repo.delete(f"role_permissions:{role_id}")

    async def _count_role_permissions(self, role_id: str) -> int:
        """统计角色权限数量"""
        stmt = select(func.count()).where(
            and_(
                self.role_permission_model.role_id == role_id,
                self.role_permission_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar() or 0

    async def _validate_users_exist(self, tenant_id: str, user_ids: List[str]) -> List[str]:
        """验证用户存在性"""
        stmt = select(self.user_model.user_id).where(
            and_(
                self.user_model.user_id.in_(user_ids),
                self.user_model.tenant_id == tenant_id,
                self.user_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        valid_user_ids = [row.user_id for row in result]

        invalid_users = [u_id for u_id in user_ids if u_id not in valid_user_ids]
        if invalid_users:
            raise NotFoundError(f"以下用户不存在: {invalid_users}")

        return valid_user_ids

    async def _check_user_role_exists(self, user_id: str, role_id: str) -> bool:
        """检查用户角色关联是否存在"""
        stmt = select(self.user_role_model).where(
            and_(
                self.user_role_model.user_id == user_id,
                self.user_role_model.role_id == role_id,
                self.user_role_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none() is not None

    async def _check_role_permission_exists(self, role_id: str, permission_id: str) -> bool:
        """检查角色权限关联是否存在"""
        stmt = select(self.role_permission_model).where(
            and_(
                self.role_permission_model.role_id == role_id,
                self.role_permission_model.permission_id == permission_id,
                self.role_permission_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none() is not None
