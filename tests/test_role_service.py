"""
角色服务测试

测试角色服务的核心功能
"""
import pytest
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

from services.iam_service.services.role_service import RoleService
from domain_common.models.iam_models import User, Tenant, Role, Permission, UserRole, RolePermission, AuditLog
from domain_common.models import CommonStatus
from commonlib.exceptions.exceptions import ValidationError, NotFoundError, DuplicateResourceError


@pytest.fixture
async def role_service():
    """创建角色服务实例"""
    session = AsyncMock()
    redis_repo = AsyncMock()
    
    service = RoleService(
        session=session,
        redis_repo=redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        audit_log_model=AuditLog
    )
    return service


@pytest.fixture
def sample_tenant():
    """示例租户"""
    return Tenant(
        tenant_id="tenant_123",
        tenant_name="测试租户",
        tenant_code="test_tenant",
        status=CommonStatus.ACTIVE
    )


@pytest.fixture
def sample_role():
    """示例角色"""
    return Role(
        role_id="role_123",
        tenant_id="tenant_123",
        role_name="管理员",
        role_code="ADMIN",
        description="系统管理员角色",
        status=CommonStatus.ACTIVE,
        level=1,
        max_users=0,
        meta_data={},
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def sample_permission():
    """示例权限"""
    return Permission(
        permission_id="perm_123",
        tenant_id="tenant_123",
        permission_name="用户管理",
        permission_code="user:manage",
        resource="user",
        action="manage",
        description="管理用户的权限",
        status=CommonStatus.ACTIVE,
        level=1,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


class TestRoleCreation:
    """测试角色创建功能"""
    
    async def test_create_role_success(self, role_service, sample_tenant):
        """测试成功创建角色"""
        # Mock dependencies
        role_service._get_tenant_by_id = AsyncMock(return_value=sample_tenant)
        role_service._validate_role_uniqueness = AsyncMock()
        role_service._validate_permissions_exist = AsyncMock(return_value=["perm_123"])
        role_service._assign_permissions_to_role = AsyncMock(return_value=[
            {
                "permission_id": "perm_123",
                "permission_name": "用户管理",
                "permission_code": "user:manage",
                "resource": "user",
                "action": "manage"
            }
        ])
        role_service._create_audit_log = AsyncMock()
        role_service._cache_role_info = AsyncMock()
        role_service.session.flush = AsyncMock()
        role_service.session.commit = AsyncMock()
        
        # Execute
        result = await role_service.create_role(
            role_name="管理员",
            role_code="ADMIN",
            description="系统管理员角色",
            tenant_id="tenant_123",
            permission_ids=["perm_123"]
        )
        
        # Verify
        assert result["role_name"] == "管理员"
        assert result["role_code"] == "ADMIN"
        assert result["status"] == CommonStatus.ACTIVE
        assert "role_id" in result
        assert len(result["permissions"]) == 1
        role_service._validate_role_uniqueness.assert_called_once()
        role_service.session.commit.assert_called_once()

    async def test_create_role_duplicate_code(self, role_service, sample_tenant):
        """测试创建重复编码的角色"""
        # Mock dependencies
        role_service._get_tenant_by_id = AsyncMock(return_value=sample_tenant)
        role_service._validate_role_uniqueness = AsyncMock(
            side_effect=DuplicateResourceError("角色编码已存在", "role_code", "ADMIN")
        )
        
        # Execute and verify exception
        with pytest.raises(DuplicateResourceError):
            await role_service.create_role(
                role_name="管理员",
                role_code="ADMIN",
                description="系统管理员角色",
                tenant_id="tenant_123"
            )


class TestRoleQuery:
    """测试角色查询功能"""
    
    async def test_list_roles_success(self, role_service, sample_tenant, sample_role):
        """测试查询角色列表"""
        # Mock dependencies
        role_service._get_tenant_by_id = AsyncMock(return_value=sample_tenant)
        role_service._get_roles_statistics = AsyncMock(return_value={
            "role_123": {"user_count": 2, "permission_count": 5}
        })
        
        # Mock database query
        mock_result = AsyncMock()
        mock_result.scalars.return_value.all.return_value = [sample_role]
        role_service.session.execute = AsyncMock(return_value=mock_result)
        
        # Mock count query
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 1
        role_service.session.execute.side_effect = [mock_result, mock_count_result]
        
        # Execute
        result = await role_service.list_roles(
            tenant_id="tenant_123",
            limit=20
        )
        
        # Verify
        assert "roles" in result
        assert result["total"] == 1
        assert len(result["roles"]) == 1
        assert result["roles"][0]["role_id"] == "role_123"
        assert result["roles"][0]["user_count"] == 2

    async def test_get_role_detail_success(self, role_service, sample_role):
        """测试获取角色详情"""
        # Mock dependencies
        role_service._get_role_by_id = AsyncMock(return_value=sample_role)
        role_service._get_role_permissions = AsyncMock(return_value=[
            {
                "permission_id": "perm_123",
                "permission_name": "用户管理",
                "permission_code": "user:manage",
                "resource": "user",
                "action": "manage"
            }
        ])
        role_service._get_child_roles = AsyncMock(return_value=[])
        role_service._count_role_users = AsyncMock(return_value=2)
        role_service._get_role_statistics = AsyncMock(return_value={
            "direct_permission_count": 1,
            "inherited_permission_count": 0
        })
        
        # Execute
        result = await role_service.get_role_detail(
            role_id="role_123",
            tenant_id="tenant_123"
        )
        
        # Verify
        assert result["role_id"] == "role_123"
        assert result["role_name"] == "管理员"
        assert result["statistics"]["user_count"] == 2
        assert len(result["permissions"]) == 1


class TestRoleUpdate:
    """测试角色更新功能"""
    
    async def test_update_role_success(self, role_service, sample_role):
        """测试更新角色信息"""
        # Mock dependencies
        role_service._get_role_by_id = AsyncMock(return_value=sample_role)
        role_service._check_role_name_uniqueness = AsyncMock()
        role_service._validate_status_transition = AsyncMock()
        role_service._clear_role_cache = AsyncMock()
        role_service._create_audit_log = AsyncMock()
        role_service._get_role_permissions = AsyncMock(return_value=[])
        role_service._count_role_users = AsyncMock(return_value=0)
        role_service.session.commit = AsyncMock()
        
        # Execute
        result = await role_service.update_role(
            role_id="role_123",
            tenant_id="tenant_123",
            role_name="超级管理员",
            description="更新后的描述"
        )
        
        # Verify
        assert result["role_id"] == "role_123"
        assert sample_role.role_name == "超级管理员"
        assert sample_role.description == "更新后的描述"
        role_service.session.commit.assert_called_once()


class TestRolePermissions:
    """测试角色权限管理功能"""
    
    async def test_assign_permissions_success(self, role_service, sample_role):
        """测试分配权限"""
        # Mock dependencies
        role_service._get_role_by_id = AsyncMock(return_value=sample_role)
        role_service._validate_permissions_exist = AsyncMock(return_value=["perm_123"])
        role_service._get_role_existing_permissions = AsyncMock(return_value=[])
        role_service._get_permission_info = AsyncMock(return_value={
            "name": "用户管理",
            "code": "user:manage",
            "resource": "user",
            "action": "manage"
        })
        role_service._create_audit_log = AsyncMock()
        role_service._clear_role_permissions_cache = AsyncMock()
        role_service.session.commit = AsyncMock()
        
        # Execute
        result = await role_service.assign_permissions(
            role_id="role_123",
            tenant_id="tenant_123",
            permission_ids=["perm_123"]
        )
        
        # Verify
        assert result["success"] is True
        assert result["role_id"] == "role_123"
        assert len(result["assigned_permissions"]) == 1
        role_service.session.commit.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
